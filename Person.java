/**
 * Base class representing a Person with basic information
 * This class serves as the parent class for Student
 */
public abstract class Person {
    protected String name;
    
    // Default constructor
    public Person() {
        this.name = "";
    }
    
    // Parameterized constructor
    public Person(String name) {
        this.name = name;
    }
    
    // Getter and setter for name
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    // Abstract method to be implemented by subclasses
    public abstract void displayDetails();
}
